openapi: 3.0.1
info:
  title: langfuse-organizations
  version: ''
  description: >-
    ## General Notes


    This admin API is only available on self-hosted instances, not on Langfuse
    Cloud.

    An administrator must set the `ADMIN_API_KEY` environment variable to use
    this API.


    ## Authentication


    Authenticate with the API by setting the Authorization header to `Bearer
    $ADMIN_API_KEY` where

    `$ADMIN_API_KEY` is the API key set via the container environment.


    ## Exports


    - OpenAPI spec:
    https://cloud.langfuse.com/generated/organizations-api/openapi.yml

    - Postman collection:
    https://cloud.langfuse.com/generated/organizations-postman/collection.json


    ## Looking for the Langfuse API?


    Are you looking for the full Langfuse API?

    Check out our [API reference](https://api.reference.langfuse.com/#overview).
paths:
  /api/admin/organizations:
    get:
      description: Get all organizations
      operationId: organizations_getAll
      tags:
        - Organizations
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Organizations'
        '400':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '401':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '403':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '405':
          description: ''
          content:
            application/json:
              schema:
                type: string
      security: &ref_0
        - BearerAuth: []
    post:
      description: Create a new organization
      operationId: organizations_create
      tags:
        - Organizations
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Organization'
        '400':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '401':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '403':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '405':
          description: ''
          content:
            application/json:
              schema:
                type: string
      security: *ref_0
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                metadata:
                  type: object
                  additionalProperties: true
                  nullable: true
                  description: Optional metadata for the organization
              required:
                - name
  /api/admin/organizations/{organizationId}:
    get:
      description: Get organization by ID
      operationId: organizations_getById
      tags:
        - Organizations
      parameters:
        - name: organizationId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Organization'
        '400':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '401':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '403':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '405':
          description: ''
          content:
            application/json:
              schema:
                type: string
      security: *ref_0
    put:
      description: Update an organization
      operationId: organizations_update
      tags:
        - Organizations
      parameters:
        - name: organizationId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Organization'
        '400':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '401':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '403':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '405':
          description: ''
          content:
            application/json:
              schema:
                type: string
      security: *ref_0
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                metadata:
                  type: object
                  additionalProperties: true
                  nullable: true
                  description: Optional metadata for the organization
              required:
                - name
    delete:
      description: Delete an organization
      operationId: organizations_delete
      tags:
        - Organizations
      parameters:
        - name: organizationId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteOrganizationResponse'
        '400':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '401':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '403':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '405':
          description: ''
          content:
            application/json:
              schema:
                type: string
      security: *ref_0
  /api/admin/organizations/{organizationId}/apiKeys:
    get:
      description: Get all API keys for an organization
      operationId: organizations_getApiKeys
      tags:
        - Organizations
      parameters:
        - name: organizationId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiKeyList'
        '400':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '401':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '403':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '405':
          description: ''
          content:
            application/json:
              schema:
                type: string
      security: *ref_0
    post:
      description: Create a new API key for an organization
      operationId: organizations_createApiKey
      tags:
        - Organizations
      parameters:
        - name: organizationId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiKeyResponse'
        '400':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '401':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '403':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '405':
          description: ''
          content:
            application/json:
              schema:
                type: string
      security: *ref_0
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                note:
                  type: string
                  nullable: true
                  description: Optional note for the API key
  /api/admin/organizations/{organizationId}/apiKeys/{apiKeyId}:
    delete:
      description: Delete an API key for an organization
      operationId: organizations_deleteApiKey
      tags:
        - Organizations
      parameters:
        - name: organizationId
          in: path
          required: true
          schema:
            type: string
        - name: apiKeyId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteOrganizationResponse'
        '400':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '401':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '403':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '405':
          description: ''
          content:
            application/json:
              schema:
                type: string
      security: *ref_0
components:
  schemas:
    Organizations:
      title: Organizations
      type: object
      description: List of organizations
      properties:
        organizations:
          type: array
          items:
            $ref: '#/components/schemas/Organization'
      required:
        - organizations
    Organization:
      title: Organization
      type: object
      description: Organization object
      properties:
        id:
          type: string
        name:
          type: string
        createdAt:
          type: string
          format: date-time
        metadata:
          type: object
          additionalProperties: true
          description: Metadata for the organization
      required:
        - id
        - name
        - createdAt
        - metadata
    DeleteOrganizationResponse:
      title: DeleteOrganizationResponse
      type: object
      description: Response for successful organization deletion
      properties:
        success:
          type: boolean
      required:
        - success
    ApiKeyList:
      title: ApiKeyList
      type: object
      description: List of API keys for an organization
      properties:
        apiKeys:
          type: array
          items:
            $ref: '#/components/schemas/ApiKeySummary'
      required:
        - apiKeys
    ApiKeySummary:
      title: ApiKeySummary
      type: object
      description: Summary of an API key
      properties:
        id:
          type: string
        createdAt:
          type: string
          format: date-time
        expiresAt:
          type: string
          format: date-time
          nullable: true
        lastUsedAt:
          type: string
          format: date-time
          nullable: true
        note:
          type: string
          nullable: true
        publicKey:
          type: string
        displaySecretKey:
          type: string
      required:
        - id
        - createdAt
        - publicKey
        - displaySecretKey
    ApiKeyResponse:
      title: ApiKeyResponse
      type: object
      description: Response for API key creation
      properties:
        id:
          type: string
        createdAt:
          type: string
          format: date-time
        publicKey:
          type: string
        secretKey:
          type: string
        displaySecretKey:
          type: string
        note:
          type: string
          nullable: true
      required:
        - id
        - createdAt
        - publicKey
        - secretKey
        - displaySecretKey
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
