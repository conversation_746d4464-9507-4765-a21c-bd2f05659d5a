name: CI

on:
  pull_request:
    branches: ["*"]
  push:
    branches: ["main", "cloud"]

# You can leverage Vercel Remote Caching with Turbo to speed up your builds
# @link https://turborepo.org/docs/core-concepts/remote-caching#remote-caching-on-vercel-builds
env:
  TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
  TURBO_TEAM: ${{ secrets.TURBO_TEAM }}

jobs:
  build-lint:
    env:
      DATABASE_URL: postgresql://postgres:postgres@localhost:5432/postgres
      SHADOW_DATABASE_URL: postgresql://postgres:postgres@localhost:5432/postgres
      DIRECT_URL: postgresql://postgres:postgres@localhost:5432/postgres
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Start containers
        run: docker compose -f "docker-compose.yml" up -d --build

      - name: Setup pnpm
        uses: pnpm/action-setup@v2.2.4

      - name: Setup Node 18
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Get pnpm store directory
        id: pnpm-cache
        run: |
          echo "pnpm_cache_dir=$(pnpm store path)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v3
        with:
          path: ${{ steps.pnpm-cache.outputs.pnpm_cache_dir }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install deps (with cache)
        run: pnpm install

      # Normally, this would be done as part of the turbo pipeline - however since the Expo app doesn't depend on `@acme/db` it doesn't care.
      # TODO: Free for all to find a better solution here.
      - name: Deploy db
        run: pnpm turbo db:deploy

      - name: Generate Prisma Client
        run: pnpm turbo db:generate

      - name: Build, lint and type-check
        run: pnpm turbo build lint type-check
        env:
          SKIP_ENV_VALIDATION: true

      # FIXME: Add this back once we have an Expo SDK supporting React 18.2
      # - name: Check workspaces
      #   run: pnpm manypkg check
