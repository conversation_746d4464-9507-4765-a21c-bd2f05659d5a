# yaml-language-server: $schema=https://raw.githubusercontent.com/fern-api/fern/main/fern.schema.json
imports:
  commons: ./commons.yml
  pagination: ./utils/pagination.yml
service:
  auth: true
  base-path: /api/public
  endpoints:
    create:
      method: POST
      docs: Create a dataset item
      path: /dataset-items
      request: CreateDatasetItemRequest
      response: commons.DatasetItem
    get:
      docs: Get a dataset item
      method: GET
      path: /dataset-items/{id}
      path-parameters:
        id:
          type: string
      response: commons.DatasetItem
    list:
      docs: Get dataset items
      method: GET
      path: /dataset-items
      request:
        name: GetDatasetItemsRequest
        query-parameters:
          datasetName: optional<string>
          sourceTraceId: optional<string>
          sourceObservationId: optional<string>
          page:
            type: optional<integer>
            docs: page number, starts at 1
          limit:
            type: optional<integer>
            docs: limit of items per page
      response: PaginatedDatasetItems
    delete:
      docs: Delete a dataset item and all its run items. This action is irreversible.
      method: DELETE
      path: /dataset-items/{id}
      path-parameters:
        id:
          type: string
      response: DeleteDatasetItemResponse

types:
  DeleteDatasetItemResponse:
    properties:
      message:
        type: string
        docs: Success message after deletion
  CreateDatasetItemRequest:
    properties:
      datasetName: string
      input: optional<unknown>
      expectedOutput: optional<unknown>
      metadata: optional<unknown>
      sourceTraceId: optional<string>
      sourceObservationId: optional<string>
      id:
        type: optional<string>
        docs: Dataset items are upserted on their id. Id needs to be unique (project-level) and cannot be reused across datasets.
      status:
        type: optional<commons.DatasetStatus>
        docs: Defaults to ACTIVE for newly created items
  PaginatedDatasetItems:
    properties:
      data: list<commons.DatasetItem>
      meta: pagination.MetaResponse
