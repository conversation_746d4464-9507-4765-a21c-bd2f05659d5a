name: langfuse
docs: |
  ## Authentication

  Authenticate with the API using [Basic Auth](https://en.wikipedia.org/wiki/Basic_access_authentication), get API keys in the project settings:

  - username: Langfuse Public Key
  - password: Langfuse Secret Key

  ## Exports

  - OpenAPI spec: https://cloud.langfuse.com/generated/api/openapi.yml
  - Postman collection: https://cloud.langfuse.com/generated/postman/collection.json

error-discrimination:
  strategy: status-code
auth: basic
imports:
  commons: commons.yml
errors:
  - commons.Error
  - commons.UnauthorizedError
  - commons.AccessDeniedError
  - commons.MethodNotAllowedError
  - commons.NotFoundError
headers:
  X-Langfuse-Sdk-Name: optional<string>
  X-Langfuse-Sdk-Version: optional<string>
  X-Langfuse-Public-Key: optional<string>
