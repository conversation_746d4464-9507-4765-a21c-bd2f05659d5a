name: langfuse-organizations
docs: |
  ## General Notes
  
  This admin API is only available on self-hosted instances, not on Langfuse Cloud.
  An administrator must set the `ADMIN_API_KEY` environment variable to use this API.
  
  ## Authentication

  Authenticate with the API by setting the Authorization header to `Bearer $ADMIN_API_KEY` where
  `$ADMIN_API_KEY` is the API key set via the container environment.
  
  ## Exports

  - OpenAPI spec: https://cloud.langfuse.com/generated/organizations-api/openapi.yml
  - Postman collection: https://cloud.langfuse.com/generated/organizations-postman/collection.json
  
  ## Looking for the Langfuse API?
  
  Are you looking for the full Langfuse API?
  Check out our [API reference](https://api.reference.langfuse.com/#overview).

error-discrimination:
  strategy: status-code
auth: bearer
imports:
  commons: commons.yml
errors:
  - commons.Error
  - commons.UnauthorizedError
  - commons.AccessDeniedError
  - commons.MethodNotAllowedError
