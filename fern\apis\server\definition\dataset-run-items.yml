# yaml-language-server: $schema=https://raw.githubusercontent.com/fern-api/fern/main/fern.schema.json
imports:
  commons: ./commons.yml
  pagination: ./utils/pagination.yml
service:
  auth: true
  base-path: /api/public
  endpoints:
    create:
      method: POST
      docs: Create a dataset run item
      path: /dataset-run-items
      request: CreateDatasetRunItemRequest
      response: commons.DatasetRunItem
    list:
      method: GET
      docs: List dataset run items
      path: /dataset-run-items
      request:
        name: ListDatasetRunItemsRequest
        query-parameters:
          datasetId: string
          runName: string
          page:
            type: optional<integer>
            docs: page number, starts at 1
          limit:
            type: optional<integer>
            docs: limit of items per page
          response: PaginatedDatasetRunItems

types:
  CreateDatasetRunItemRequest:
    properties:
      runName: string
      runDescription:
        type: optional<string>
        docs: Description of the run. If run exists, description will be updated.
      metadata:
        type: optional<unknown>
        docs: Metadata of the dataset run, updates run if run already exists
      datasetItemId: string
      observationId: optional<string>
      traceId:
        type: optional<string>
        docs: traceId should always be provided. For compatibility with older SDK versions it can also be inferred from the provided observationId.
  PaginatedDatasetRunItems:
    properties:
      data: list<commons.DatasetRunItem>
      meta: pagination.MetaResponse
