openapi: 3.0.1
info:
  title: langfuse
  version: ''
paths:
  /api/public/scores:
    post:
      description: Add a score to the database, upserts on id
      operationId: score_create
      tags:
        - Score
      parameters: []
      responses:
        '204':
          description: ''
        '400':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '401':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '403':
          description: ''
          content:
            application/json:
              schema:
                type: string
        '405':
          description: ''
          content:
            application/json:
              schema:
                type: string
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateScoreRequest'
components:
  schemas:
    CreateScoreRequest:
      title: CreateScoreRequest
      type: object
      properties:
        id:
          type: string
          nullable: true
        traceId:
          type: string
          nullable: true
        sessionId:
          type: string
          nullable: true
        observationId:
          type: string
          nullable: true
        datasetRunId:
          type: string
          nullable: true
        name:
          type: string
          example: novelty
        value:
          $ref: '#/components/schemas/CreateScoreValue'
          description: >-
            The value of the score. Must be passed as string for categorical
            scores, and numeric for boolean and numeric scores. Boolean score
            values must equal either 1 or 0 (true or false)
        comment:
          type: string
          nullable: true
        metadata:
          nullable: true
        dataType:
          $ref: '#/components/schemas/ScoreDataType'
          nullable: true
          description: >-
            When set, must match the score value's type. If not set, will be
            inferred from the score value or config
        configId:
          type: string
          nullable: true
          description: >-
            Reference a score config on a score. When set, the score name must
            equal the config name and scores must comply with the config's range
            and data type. For categorical scores, the value must map to a
            config category. Numeric scores might be constrained by the score
            config's max and min values
      required:
        - name
        - value
    ScoreDataType:
      title: ScoreDataType
      type: string
      enum:
        - NUMERIC
        - CATEGORICAL
        - BOOLEAN
    CreateScoreValue:
      title: CreateScoreValue
      oneOf:
        - type: number
          format: double
        - type: string
      description: >-
        The value of the score. Must be passed as string for categorical scores,
        and numeric for boolean and numeric scores
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
