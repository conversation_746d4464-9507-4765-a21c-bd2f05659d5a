# yaml-language-server: $schema=https://raw.githubusercontent.com/fern-api/fern/main/fern.schema.json
imports:
  pagination: ./utils/pagination.yml
  commons: ./commons.yml
service:
  auth: true
  base-path: /api/public
  endpoints:
    create:
      docs: Create a score configuration (config). Score configs are used to define the structure of scores
      method: POST
      path: /score-configs
      request: CreateScoreConfigRequest
      response: commons.ScoreConfig
    get:
      docs: Get all score configs
      method: GET
      path: /score-configs
      request:
        name: GetScoreConfigsRequest
        query-parameters:
          page:
            type: optional<integer>
            docs: Page number, starts at 1.
          limit:
            type: optional<integer>
            docs: Limit of items per page. If you encounter api issues due to too large page sizes, try to reduce the limit
      response: ScoreConfigs
    get-by-id:
      docs: Get a score config
      method: GET
      path: /score-configs/{configId}
      path-parameters:
        configId:
          type: string
          docs: The unique langfuse identifier of a score config
      response: commons.ScoreConfig
types:
  ScoreConfigs:
    properties:
      data: list<commons.ScoreConfig>
      meta: pagination.MetaResponse
  CreateScoreConfigRequest:
    properties:
      name: string
      dataType: commons.ScoreDataType
      categories:
        type: optional<list<commons.ConfigCategory>>
        docs: Configure custom categories for categorical scores. Pass a list of objects with `label` and `value` properties. Categories are autogenerated for boolean configs and cannot be passed
      minValue:
        type: optional<double>
        docs: Configure a minimum value for numerical scores. If not set, the minimum value defaults to -∞
      maxValue:
        type: optional<double>
        docs: Configure a maximum value for numerical scores. If not set, the maximum value defaults to +∞
      description:
        type: optional<string>
        docs: Description is shown across the Langfuse UI and can be used to e.g. explain the config categories in detail, why a numeric range was set, or provide additional context on config name or usage
